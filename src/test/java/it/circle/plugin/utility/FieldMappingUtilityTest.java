package it.circle.plugin.utility;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class FieldMappingUtilityTest {

    @Test
    void testNormalizeFieldName_OrderFields() {
        assertEquals("order_id", FieldMappingUtility.normalizeFieldName("orderid"));
        assertEquals("order_id", FieldMappingUtility.normalizeFieldName("order_id"));
        assertEquals("order_id", FieldMappingUtility.normalizeFieldName("_id"));
        assertEquals("order_id", FieldMappingUtility.normalizeFieldName("id"));
        
        assertEquals("total_paid", FieldMappingUtility.normalizeFieldName("totalpaid"));
        assertEquals("total_paid", FieldMappingUtility.normalizeFieldName("total_paid"));
        
        assertEquals("order_date", FieldMappingUtility.normalizeFieldName("orderdate"));
        assertEquals("order_date", FieldMappingUtility.normalizeFieldName("order_date"));
    }

    @Test
    void testNormalizeFieldName_ContactFields() {
        assertEquals("first_name", FieldMappingUtility.normalizeFieldName("firstName"));
        assertEquals("first_name", FieldMappingUtility.normalizeFieldName("firstname"));
        assertEquals("first_name", FieldMappingUtility.normalizeFieldName("first_name"));
        
        assertEquals("last_name", FieldMappingUtility.normalizeFieldName("lastName"));
        assertEquals("last_name", FieldMappingUtility.normalizeFieldName("lastname"));
        
        assertEquals("email", FieldMappingUtility.normalizeFieldName("email_address"));
        assertEquals("email", FieldMappingUtility.normalizeFieldName("primary_email"));
    }

    @Test
    void testNormalizeFieldName_UnknownField() {
        String unknownField = "someUnknownField";
        assertEquals(unknownField.toLowerCase(), FieldMappingUtility.normalizeFieldName(unknownField));
    }

    @Test
    void testNormalizeFieldName_NullAndEmpty() {
        assertNull(FieldMappingUtility.normalizeFieldName(null));
        assertEquals("", FieldMappingUtility.normalizeFieldName(""));
        assertEquals("", FieldMappingUtility.normalizeFieldName("   "));
    }

    @Test
    void testAreFieldNamesEquivalent() {
        assertTrue(FieldMappingUtility.areFieldNamesEquivalent("orderid", "order_id"));
        assertTrue(FieldMappingUtility.areFieldNamesEquivalent("firstName", "first_name"));
        assertTrue(FieldMappingUtility.areFieldNamesEquivalent("email", "email_address"));
        assertTrue(FieldMappingUtility.areFieldNamesEquivalent("id", "_id"));
        
        assertFalse(FieldMappingUtility.areFieldNamesEquivalent("firstName", "lastName"));
        assertFalse(FieldMappingUtility.areFieldNamesEquivalent("email", "phone"));
        
        assertFalse(FieldMappingUtility.areFieldNamesEquivalent(null, "field"));
        assertFalse(FieldMappingUtility.areFieldNamesEquivalent("field", null));
        assertFalse(FieldMappingUtility.areFieldNamesEquivalent(null, null));
    }

    @Test
    void testGetFieldAliases() {
        Set<String> orderIdAliases = FieldMappingUtility.getFieldAliases("order_id");
        assertTrue(orderIdAliases.contains("order_id"));
        assertTrue(orderIdAliases.contains("orderid"));
        assertTrue(orderIdAliases.contains("_id"));
        assertTrue(orderIdAliases.contains("id"));
        
        Set<String> emailAliases = FieldMappingUtility.getFieldAliases("email");
        assertTrue(emailAliases.contains("email"));
        assertTrue(emailAliases.contains("email_address"));
        assertTrue(emailAliases.contains("primary_email"));
        
        Set<String> unknownAliases = FieldMappingUtility.getFieldAliases("unknownField");
        assertEquals(1, unknownAliases.size());
        assertTrue(unknownAliases.contains("unknownfield"));
    }

    @Test
    void testFindBestMatchingField_ExactMatch() {
        List<String> availableFields = Arrays.asList("firstName", "lastName", "email", "phone");
        
        Optional<String> result = FieldMappingUtility.findBestMatchingField("firstName", availableFields);
        assertTrue(result.isPresent());
        assertEquals("firstName", result.get());
        
        result = FieldMappingUtility.findBestMatchingField("email", availableFields);
        assertTrue(result.isPresent());
        assertEquals("email", result.get());
    }

    @Test
    void testFindBestMatchingField_AliasMatch() {
        List<String> availableFields = Arrays.asList("first_name", "last_name", "email_address", "phone_number");
        
        Optional<String> result = FieldMappingUtility.findBestMatchingField("firstName", availableFields);
        assertTrue(result.isPresent());
        assertEquals("first_name", result.get());
        
        result = FieldMappingUtility.findBestMatchingField("email", availableFields);
        assertTrue(result.isPresent());
        assertEquals("email_address", result.get());
        
        result = FieldMappingUtility.findBestMatchingField("phone", availableFields);
        assertTrue(result.isPresent());
        assertEquals("phone_number", result.get());
    }

    @Test
    void testFindBestMatchingField_NoMatch() {
        List<String> availableFields = Arrays.asList("firstName", "lastName", "email");
        
        Optional<String> result = FieldMappingUtility.findBestMatchingField("address", availableFields);
        assertTrue(result.isEmpty());
        
        result = FieldMappingUtility.findBestMatchingField("unknownField", availableFields);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindBestMatchingField_NullAndEmpty() {
        List<String> availableFields = Arrays.asList("firstName", "lastName");
        
        Optional<String> result = FieldMappingUtility.findBestMatchingField(null, availableFields);
        assertTrue(result.isEmpty());
        
        result = FieldMappingUtility.findBestMatchingField("firstName", null);
        assertTrue(result.isEmpty());
        
        result = FieldMappingUtility.findBestMatchingField("firstName", Arrays.asList());
        assertTrue(result.isEmpty());
    }

    @Test
    void testNormalizeFieldNames() {
        List<String> inputFields = Arrays.asList("firstName", "lastName", "orderid", "email_address", null, "");
        List<String> result = FieldMappingUtility.normalizeFieldNames(inputFields);
        
        assertNotNull(result);
        assertTrue(result.contains("first_name"));
        assertTrue(result.contains("last_name"));
        assertTrue(result.contains("order_id"));
        assertTrue(result.contains("email"));
        assertFalse(result.contains(null));
        
        // Check that duplicates are removed
        assertEquals(result.size(), result.stream().distinct().count());
    }

    @Test
    void testNormalizeFieldNames_NullAndEmpty() {
        List<String> result = FieldMappingUtility.normalizeFieldNames(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        result = FieldMappingUtility.normalizeFieldNames(Arrays.asList());
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
