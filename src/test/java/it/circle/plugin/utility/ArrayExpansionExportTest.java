package it.circle.plugin.utility;

import it.circle.plugin.model.FieldMetadata;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to demonstrate and validate the array expansion functionality
 * for Excel export workflow as described in the technical requirements.
 */
@ExtendWith(MockitoExtension.class)
class ArrayExpansionExportTest {

    private Map<String, FieldMetadata> fieldMetadataMap;
    private List<MockOrderDocument> mockOrders;

    @BeforeEach
    void setUp() {
        setupFieldMetadataMap();
        setupMockOrders();
    }

    /**
     * Test Step 2.3: Retrieve Entity Structure from SettingEntity
     * Demonstrates building field metadata map with type information
     */
    @Test
    void testBuildFieldMetadataMap() {
        // Given - field metadata map is set up in @BeforeEach
        
        // When - validate the structure
        assertNotNull(fieldMetadataMap);
        assertTrue(fieldMetadataMap.containsKey("_id"));
        assertTrue(fieldMetadataMap.containsKey("total_products_tax_incl"));
        assertTrue(fieldMetadataMap.containsKey("OrderRow"));
        assertTrue(fieldMetadataMap.containsKey("OrderRow.product_name"));
        
        // Then - verify field types
        FieldMetadata idField = fieldMetadataMap.get("_id");
        assertEquals("ObjectId", idField.getType());
        assertTrue(idField.isPrimitiveType());
        
        FieldMetadata totalField = fieldMetadataMap.get("total_products_tax_incl");
        assertEquals("BigDecimal", totalField.getType());
        assertTrue(totalField.isPrimitiveType());
        
        FieldMetadata orderRowField = fieldMetadataMap.get("OrderRow");
        assertEquals("List", orderRowField.getType());
        assertTrue(orderRowField.isArrayType());
        assertTrue(orderRowField.hasChildren());
        
        FieldMetadata productNameField = fieldMetadataMap.get("OrderRow.product_name");
        assertEquals("String", productNameField.getType());
        assertTrue(productNameField.isPrimitiveType());
    }

    /**
     * Test Step 3: Prepare Data for Export with Array Expansion
     * Demonstrates the tabular output format described in requirements
     */
    @Test
    void testArrayExpansionConversion() {
        // Given
        List<String> requestedFields = Arrays.asList("_id", "total_products_tax_incl", "OrderRow.product_name");
        
        // When
        List<Map<String, Object>> exportData = ExportUtility.convertEntityToExportDataWithArrayExpansion(
            mockOrders, requestedFields, "Order", "test-instance", fieldMetadataMap);
        
        // Then - verify array expansion results
        assertNotNull(exportData);
        assertEquals(3, exportData.size()); // ORD123 has 2 products, ORD124 has 1 product = 3 rows total
        
        // Verify first row (ORD123 - Product A)
        Map<String, Object> row1 = exportData.get(0);
        assertEquals("ORD123", row1.get("_id"));
        assertEquals(100.00, row1.get("total_products_tax_incl"));
        assertEquals("Product A", row1.get("OrderRow.product_name"));
        
        // Verify second row (ORD123 - Product B) - main entity data replicated
        Map<String, Object> row2 = exportData.get(1);
        assertEquals("ORD123", row2.get("_id"));
        assertEquals(100.00, row2.get("total_products_tax_incl"));
        assertEquals("Product B", row2.get("OrderRow.product_name"));
        
        // Verify third row (ORD124 - Product X)
        Map<String, Object> row3 = exportData.get(2);
        assertEquals("ORD124", row3.get("_id"));
        assertEquals(80.00, row3.get("total_products_tax_incl"));
        assertEquals("Product X", row3.get("OrderRow.product_name"));
    }

    /**
     * Test field validation against SettingsEntity metadata
     */
    @Test
    void testFieldValidation() {
        // Given
        List<String> requestedFields = Arrays.asList("_id", "invalid_field", "OrderRow.product_name", "another_invalid");
        
        // When
        List<String> validFields = ExportUtility.validateAndFilterFields(
            requestedFields, "Order", "test-instance", fieldMetadataMap);
        
        // Then
        assertNotNull(validFields);
        assertEquals(2, validFields.size());
        assertTrue(validFields.contains("_id"));
        assertTrue(validFields.contains("OrderRow.product_name"));
        assertFalse(validFields.contains("invalid_field"));
        assertFalse(validFields.contains("another_invalid"));
    }

    /**
     * Test type-aware field value formatting
     */
    @Test
    void testFieldValueFormatting() {
        // Given
        FieldMetadata bigDecimalField = fieldMetadataMap.get("total_products_tax_incl");
        FieldMetadata stringField = fieldMetadataMap.get("OrderRow.product_name");
        
        // When & Then - test BigDecimal formatting
        Object formattedDecimal = ExportUtility.formatFieldValueByType(100.50, bigDecimalField);
        assertEquals(100.50, formattedDecimal);
        
        // When & Then - test String formatting
        Object formattedString = ExportUtility.formatFieldValueByType("Product Name", stringField);
        assertEquals("Product Name", formattedString);
        
        // When & Then - test null handling
        Object formattedNull = ExportUtility.formatFieldValueByType(null, stringField);
        assertNull(formattedNull);
    }

    /**
     * Test conversion without array expansion (standard behavior)
     */
    @Test
    void testStandardConversionWithoutArrays() {
        // Given
        List<String> requestedFields = Arrays.asList("_id", "total_products_tax_incl");
        
        // When
        List<Map<String, Object>> exportData = ExportUtility.convertEntityToExportData(
            mockOrders, requestedFields, "Order", "test-instance");
        
        // Then - should have one row per entity (no expansion)
        assertNotNull(exportData);
        assertEquals(2, exportData.size()); // One row per order
        
        Map<String, Object> row1 = exportData.get(0);
        assertEquals("ORD123", row1.get("_id"));
        assertEquals(100.00, row1.get("total_products_tax_incl"));
        
        Map<String, Object> row2 = exportData.get(1);
        assertEquals("ORD124", row2.get("_id"));
        assertEquals(80.00, row2.get("total_products_tax_incl"));
    }

    /**
     * Test empty array handling
     */
    @Test
    void testEmptyArrayHandling() {
        // Given - order with empty OrderRow array
        MockOrderDocument emptyOrder = new MockOrderDocument("ORD125", 50.00, new ArrayList<>());
        List<MockOrderDocument> ordersWithEmpty = Arrays.asList(emptyOrder);
        List<String> requestedFields = Arrays.asList("_id", "total_products_tax_incl", "OrderRow.product_name");
        
        // When
        List<Map<String, Object>> exportData = ExportUtility.convertEntityToExportDataWithArrayExpansion(
            ordersWithEmpty, requestedFields, "Order", "test-instance", fieldMetadataMap);
        
        // Then - should create one row with null array values
        assertNotNull(exportData);
        assertEquals(1, exportData.size());
        
        Map<String, Object> row = exportData.get(0);
        assertEquals("ORD125", row.get("_id"));
        assertEquals(50.00, row.get("total_products_tax_incl"));
        assertNull(row.get("OrderRow.product_name"));
    }

    // Helper methods to set up test data

    private void setupFieldMetadataMap() {
        fieldMetadataMap = new HashMap<>();
        
        // Create primitive fields
        fieldMetadataMap.put("_id", FieldMetadata.createPrimitive("_id", "ObjectId"));
        fieldMetadataMap.put("total_products_tax_incl", FieldMetadata.createPrimitive("total_products_tax_incl", "BigDecimal"));
        
        // Create array field with children
        List<FieldMetadata> orderRowChildren = Arrays.asList(
            FieldMetadata.createPrimitive("product_name", "String"),
            FieldMetadata.createPrimitive("quantity", "Integer"),
            FieldMetadata.createPrimitive("price", "BigDecimal")
        );
        FieldMetadata orderRowField = FieldMetadata.createArray("OrderRow", orderRowChildren);
        fieldMetadataMap.put("OrderRow", orderRowField);
        
        // Add nested field paths
        fieldMetadataMap.put("OrderRow.product_name", FieldMetadata.createPrimitive("product_name", "String"));
        fieldMetadataMap.put("OrderRow.quantity", FieldMetadata.createPrimitive("quantity", "Integer"));
        fieldMetadataMap.put("OrderRow.price", FieldMetadata.createPrimitive("price", "BigDecimal"));
    }

    private void setupMockOrders() {
        // Create mock order rows
        List<MockOrderRow> order123Rows = Arrays.asList(
            new MockOrderRow("Product A", 2, 25.00),
            new MockOrderRow("Product B", 1, 50.00)
        );
        
        List<MockOrderRow> order124Rows = Arrays.asList(
            new MockOrderRow("Product X", 3, 26.67)
        );
        
        // Create mock orders
        mockOrders = Arrays.asList(
            new MockOrderDocument("ORD123", 100.00, order123Rows),
            new MockOrderDocument("ORD124", 80.00, order124Rows)
        );
    }

    // Mock classes for testing
    public static class MockOrderDocument {
        private String id;
        private Double totalProductsTaxIncl;
        private List<MockOrderRow> orderRow;

        public MockOrderDocument(String id, Double totalProductsTaxIncl, List<MockOrderRow> orderRow) {
            this.id = id;
            this.totalProductsTaxIncl = totalProductsTaxIncl;
            this.orderRow = orderRow;
        }

        // Getters
        public String getId() { return id; }
        public Double getTotalProductsTaxIncl() { return totalProductsTaxIncl; }
        public List<MockOrderRow> getOrderRow() { return orderRow; }
    }

    public static class MockOrderRow {
        private String productName;
        private Integer quantity;
        private Double price;

        public MockOrderRow(String productName, Integer quantity, Double price) {
            this.productName = productName;
            this.quantity = quantity;
            this.price = price;
        }

        // Getters
        public String getProductName() { return productName; }
        public Integer getQuantity() { return quantity; }
        public Double getPrice() { return price; }
    }
}
