package it.circle.plugin.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import it.circle.plugin.model.ExportMessage;
import it.circle.plugin.model.Target;
import it.circle.plugin.model.Filter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest
class OrderExporterServiceImplTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testExportMessageParsing_WithFilters() throws Exception {
        // Test case 1: With filters (your original JSON)
        String jsonWithFilters = """
            {
              "owner": "<EMAIL>",
              "companyName": "ACME Spa",
              "instanceId": "acme-01",
              "exportId": "660d6e7f8fd3f23e4c112d32",
              "createdAt": "2025-07-06T14:23:00Z",
              "targets": [
                {
                  "entity": "Order",
                  "fields": ["reference", "total_paid", "order_date"],
                  "filters": [
                    {
                      "field": "status",
                      "operator": "eq",
                      "values": "PAID"
                    }
                  ]
                }
              ]
            }
            """;

        ExportMessage exportMessage = objectMapper.readValue(jsonWithFilters, ExportMessage.class);
        
        assertNotNull(exportMessage);
        assertEquals("acme-01", exportMessage.getInstanceId());
        assertEquals("<EMAIL>", exportMessage.getOwner());
        assertEquals(1, exportMessage.getTargets().size());
        
        Target target = exportMessage.getTargets().get(0);
        assertEquals("Order", target.getEntity());
        assertEquals(3, target.getFields().size());
        assertEquals(1, target.getFilters().size());
        
        Filter filter = target.getFilters().get(0);
        assertEquals("status", filter.getField());
        assertEquals("eq", filter.getOperator());
        assertEquals("PAID", filter.getValues());
        
        log.info("✅ Test passed: Export message with filters parsed correctly");
    }

    @Test
    void testExportMessageParsing_WithoutFilters() throws Exception {
        // Test case 2: Without filters - should export all orders with only specified fields
        String jsonWithoutFilters = """
            {
              "owner": "<EMAIL>",
              "instanceId": "acme-01",
              "targets": [
                {
                  "entity": "Order",
                  "fields": ["reference", "total_paid", "order_date"],
                  "filters": []
                }
              ]
            }
            """;

        ExportMessage exportMessage = objectMapper.readValue(jsonWithoutFilters, ExportMessage.class);
        
        assertNotNull(exportMessage);
        Target target = exportMessage.getTargets().get(0);
        assertEquals("Order", target.getEntity());
        assertEquals(3, target.getFields().size());
        assertTrue(target.getFilters().isEmpty()); // No filters
        
        log.info("✅ Test passed: Export message without filters parsed correctly");
    }

    @Test
    void testExportMessageParsing_NoFiltersProperty() throws Exception {
        // Test case 3: No filters property at all
        String jsonNoFiltersProperty = """
            {
              "owner": "<EMAIL>",
              "instanceId": "acme-01",
              "targets": [
                {
                  "entity": "Order",
                  "fields": ["reference", "total_paid", "order_date"]
                }
              ]
            }
            """;

        ExportMessage exportMessage = objectMapper.readValue(jsonNoFiltersProperty, ExportMessage.class);
        
        assertNotNull(exportMessage);
        Target target = exportMessage.getTargets().get(0);
        assertEquals("Order", target.getEntity());
        assertEquals(3, target.getFields().size());
        
        // When filters property is missing, it should be null or empty
        assertTrue(target.getFilters() == null || target.getFilters().isEmpty());
        
        log.info("✅ Test passed: Export message with no filters property parsed correctly");
    }

    @Test
    void testExportMessageParsing_OnlyFieldsNoFilters() throws Exception {
        // Test case 4: Only fields specified, no filters - should export all data with only these fields
        String jsonOnlyFields = """
            {
              "owner": "<EMAIL>",
              "instanceId": "test-01",
              "targets": [
                {
                  "entity": "Order",
                  "fields": ["reference", "status"]
                }
              ]
            }
            """;

        ExportMessage exportMessage = objectMapper.readValue(jsonOnlyFields, ExportMessage.class);
        
        assertNotNull(exportMessage);
        Target target = exportMessage.getTargets().get(0);
        assertEquals("Order", target.getEntity());
        assertEquals(2, target.getFields().size());
        assertTrue(target.getFields().contains("reference"));
        assertTrue(target.getFields().contains("status"));
        
        log.info("✅ Test passed: Export message with only fields (no filters) parsed correctly");
    }

    @Test
    void testExportMessageParsing_MultipleFilters() throws Exception {
        // Test case 5: Multiple filters
        String jsonMultipleFilters = """
            {
              "owner": "<EMAIL>",
              "instanceId": "test-01",
              "targets": [
                {
                  "entity": "Order",
                  "fields": ["reference", "total_paid", "order_date", "status"],
                  "filters": [
                    {
                      "field": "status",
                      "operator": "in",
                      "values": "PAID,COMPLETED"
                    },
                    {
                      "field": "order_date",
                      "operator": "gte",
                      "values": "2024-01-01T00:00:00Z"
                    }
                  ]
                }
              ]
            }
            """;

        ExportMessage exportMessage = objectMapper.readValue(jsonMultipleFilters, ExportMessage.class);
        
        assertNotNull(exportMessage);
        Target target = exportMessage.getTargets().get(0);
        assertEquals("Order", target.getEntity());
        assertEquals(4, target.getFields().size());
        assertEquals(2, target.getFilters().size());
        
        // Check first filter
        Filter statusFilter = target.getFilters().get(0);
        assertEquals("status", statusFilter.getField());
        assertEquals("in", statusFilter.getOperator());
        assertEquals("PAID,COMPLETED", statusFilter.getValues()); // Comma-separated for "in" operator

        // Check second filter
        Filter dateFilter = target.getFilters().get(1);
        assertEquals("order_date", dateFilter.getField());
        assertEquals("gte", dateFilter.getOperator());
        assertEquals("2024-01-01T00:00:00Z", dateFilter.getValues());
        
        log.info("✅ Test passed: Export message with multiple filters parsed correctly");
    }

    @Test
    void testFilterHandlingScenarios() {
        // Test different filter scenarios that the service should handle
        
        // Scenario 1: Null filters
        Target targetNullFilters = new Target("Order", Arrays.asList("reference", "status"), null);
        assertTrue(targetNullFilters.getFilters() == null);
        log.info("✅ Scenario 1: Null filters - should fetch all orders");
        
        // Scenario 2: Empty filters
        Target targetEmptyFilters = new Target("Order", Arrays.asList("reference", "status"), Collections.emptyList());
        assertTrue(targetEmptyFilters.getFilters().isEmpty());
        log.info("✅ Scenario 2: Empty filters - should fetch all orders");
        
        // Scenario 3: With filters
        Filter filter = new Filter("status", "eq", "PAID");
        Target targetWithFilters = new Target("Order", Arrays.asList("reference", "status"), Arrays.asList(filter));
        assertFalse(targetWithFilters.getFilters().isEmpty());
        log.info("✅ Scenario 3: With filters - should apply filtering");
    }
}
