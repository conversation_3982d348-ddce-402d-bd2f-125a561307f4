package it.circle.plugin.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Filter {

    @JsonProperty("field")
    private String field;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("values")
    private String values;
    
//    @JsonProperty("from")
//    private String from;
//    
//    @JsonProperty("to")
//    private String to;
    
}
