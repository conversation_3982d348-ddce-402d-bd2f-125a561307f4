package it.circle.plugin.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Fields {

    @JsonProperty("fieldsHeader")
    private List<String> fieldsHeader;

    @JsonProperty("fieldsLinee")
    private List<FieldsLinee> fieldsLinee;
}
