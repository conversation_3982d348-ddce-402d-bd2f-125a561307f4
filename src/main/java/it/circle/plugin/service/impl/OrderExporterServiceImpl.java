package it.circle.plugin.service.impl;

import it.circle.crm.data.order.document.OrderDocument;
import it.circle.crm.data.settings.entity.document.SettingsEntityDocument;
import it.circle.crm.data.settings.entity.document.SettingsEntityDocument;
import it.circle.plugin.model.*;
import it.circle.plugin.repository.OrderDocumentRepository;
import it.circle.plugin.repository.SettingsEntityRepository;
import it.circle.plugin.repository.SettingsEntityRepository;
import it.circle.plugin.service.OrderExporterService;
import it.circle.plugin.service.ExcelExportService;
import it.circle.plugin.utility.QueryBuilderUtility;
import it.circle.plugin.utility.ExportUtility;
import it.circle.plugin.utility.ReflectionFieldUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderExporterServiceImpl implements OrderExporterService {

    private final OrderDocumentRepository orderRepository;
    private final ExcelExportService excelExportService;
    private final MongoTemplate mongoTemplate;
    private final SettingsEntityRepository settingsEntityRepository;

    @Override
    public void processOrderExport(String instanceId, String owner, Target target) {
        log.info("Processing Order export for instance: {}, owner: {}", instanceId, owner);

        // Safe logging for filters count
        int filtersCount = (target != null && target.getFilters() != null) ? target.getFilters().size() : 0;
        Fields fields = target.getFields();
        log.info("Target header fields: {}, line entities: {}, filters count: {}",
            fields != null ? fields.getFieldsHeader() : null,
            fields != null && fields.getFieldsLinee() != null ? fields.getFieldsLinee().size() : 0,
            filtersCount);

        try {
            // Step 1: Handle filters - build query criteria
            List<Filter> filters = (target != null && target.getFilters() != null) ? target.getFilters() : null;

            Query query;
            if (filters == null || filters.isEmpty()) {
                // No filters provided - fetch all orders for the instance (only apply instanceId filter)
                log.info("No filters provided, fetching all orders for instance: {}", instanceId);
                query = new Query();
                if (instanceId != null && !instanceId.isEmpty()) {
                    query.addCriteria(Criteria.where("instanceId").is(instanceId));
                }
            } else {
                // Filters provided - use exact field names from RabbitMQ message
                log.info("Building query with {} filters", filters.size());
                query = QueryBuilderUtility.buildQuery(filters, instanceId);
            }

            log.info("Final MongoDB query: {}", query);

            // Step 2: Fetch order data from database
            List<OrderDocument> orders = mongoTemplate.find(query, OrderDocument.class);
            log.info("Found {} orders matching criteria", orders.size());

            if (orders.isEmpty()) {
                log.warn("No orders found for the given criteria!");
                ExportUtility.logExportSummary("Order", instanceId, owner, 0, "No orders found");
                return;
            }


            // Step 6: Generate Excel file
            String fileName = ExportUtility.generateFileName("Order", instanceId, owner);
            ByteArrayOutputStream excelFile = excelExportService.generateExcelFile(
                exportData, "Orders", fileName
            );

            // Step 7: Save Excel file
            String projectDir = System.getProperty("user.dir");
            String exportPath = projectDir + "/exports";
            String savedPath = ExportUtility.saveExcelFile(excelFile, fileName, exportPath);
            log.info("Excel file saved in project directory: {}", savedPath);

            // Step 8: Log export summary
            ExportUtility.logExportSummary("Order", instanceId, owner, exportData.size(), fileName);

            // Step 9: Log what was actually exported
            if (!exportData.isEmpty()) {
                Set<String> actualFields = exportData.get(0).keySet();
                log.info("Exported fields in Excel: {}", actualFields);
            }

            log.info("Successfully completed Order export for {} records with header/line structure", exportData.size());

        } catch (Exception e) {
            log.error("Error processing Order export for instance: {}", instanceId, e);
            throw new RuntimeException("Failed to process Order export", e);
        }
    }

}
