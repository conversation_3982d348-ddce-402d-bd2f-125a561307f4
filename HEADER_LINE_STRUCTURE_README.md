# Header/Line Export Structure Implementation

## Overview

This document describes the implementation of the new header/line export structure that separates main entity fields (headers) from array entity fields (lines) for better Excel export organization.

## New JSON Structure

### Input Format
```json
{
  "owner": "<EMAIL>",
  "companyName": "virtual",
  "instanceId": "654643cc6d83356eb0c527bc",
  "targets": [
    {
      "entity": "Order",
      "fields": {
        "fieldsHeader": ["company_id", "_id", "total_products_tax_incl"],
        "fieldsLinee": [
          {
            "entity": "OrderRow",
            "fields": ["product_name", "total_tax_incl", "quantity_ordered"]
          }
        ]
      },
      "filters": [
        {
          "field": "_id",
          "operator": "eq",
          "values": "65b4567025bd0746788ba09f"
        }
      ]
    }
  ]
}
```

### Key Changes
- **fieldsHeader**: Main entity fields (Order level) - replicated across all rows
- **fieldsLinee**: Array of line entity configurations, each with:
  - **entity**: The array entity type (e.g., "OrderRow")
  - **fields**: Fields to extract from each array element

## Implementation Components

### 1. Updated Models

#### Fields.java
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Fields {
    @JsonProperty("fieldsHeader")
    private List<String> fieldsHeader;
    
    @JsonProperty("fieldsLinee")
    private List<FieldsLinee> fieldsLinee;
}
```

#### FieldsLinee.java
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FieldsLinee {
    @JsonProperty("entity")
    private String entity;
    
    @JsonProperty("fields")
    private List<String> fields;
}
```

### 2. Reflection-Based Field Validation

#### ReflectionFieldUtility.java
- **getAvailableFields()**: Discovers all fields in an entity using reflection
- **validateFields()**: Validates requested fields against actual entity structure
- **getFieldValue()**: Extracts field values using various naming conventions
- **findArrayFields()**: Identifies array/collection fields in entities

### 3. Updated OrderExporterService

#### Key Methods
- **validateFieldsStructure()**: Validates header and line fields using reflection
- **generateExcelDataWithHeaderLines()**: Creates Excel rows with header/line structure
- **extractHeaderData()**: Extracts main entity field values
- **generateLineRows()**: Creates multiple rows for each array element
- **findArrayFieldForEntity()**: Maps entity types to actual array field names

## Processing Flow

### Step 1: Query and Fetch Data
- Build MongoDB query using filters
- Fetch Order documents from database

### Step 2: Field Validation
- Use reflection to discover available fields in OrderDocument
- Validate header fields against Order entity structure
- For each line configuration:
  - Find corresponding array field in Order entity
  - Validate line fields against array element structure

### Step 3: Excel Data Generation
For each Order:
1. Extract header data using `fieldsHeader`
2. For each `fieldsLinee` configuration:
   - Find the corresponding array field (e.g., orderRow, items, products)
   - For each array element:
     - Create new Excel row
     - Add header data (replicated)
     - Add line data from array element

### Step 4: Excel File Generation
- Generate Excel file with combined header/line data
- Save to exports directory

## Example Output

For an Order with 2 OrderRow items, the Excel output would be:

| company_id | _id | total_products_tax_incl | product_name | total_tax_incl | quantity_ordered |
|------------|-----|-------------------------|--------------|----------------|------------------|
| COMP123    | ORD1| 100.00                  | Product A    | 25.00          | 2                |
| COMP123    | ORD1| 100.00                  | Product B    | 75.00          | 1                |

## Field Name Mapping

The reflection utility supports multiple naming conventions:
- **camelCase**: `productName`
- **snake_case**: `product_name`
- **Direct field access**: If getter methods fail
- **Multiple getter patterns**: `getProductName()`, `isProductName()`

## Array Field Detection

For OrderRow entity, the system tries these field names:
- `orderRow`
- `orderRows`
- `items`
- `orderItems`
- `products`
- `lines`

## Testing

### Test Endpoints
- **POST /api/test-export/order**: Test the new structure
- **GET /api/test-export/health**: Health check

### Test Classes
- **NewStructureTest.java**: Validates JSON parsing
- **ReflectionFieldUtilityTest.java**: Tests field validation logic

## Error Handling

### Validation Errors
- Invalid header fields are logged and filtered out
- Invalid line fields are logged and filtered out
- Missing array fields result in single row with null line values

### Runtime Errors
- Empty arrays create single row with null line values
- Reflection failures fall back to null values
- Processing errors are logged and don't stop the export

## Benefits

1. **Clear Separation**: Header vs line fields are clearly separated
2. **Flexible Structure**: Supports multiple line entity types
3. **Reflection-Based**: Automatically adapts to entity structure changes
4. **Robust Validation**: Validates fields against actual entity structure
5. **Error Resilient**: Graceful handling of missing or invalid fields

## Usage Example

```bash
curl -X POST http://localhost:8080/api/test-export/order \
  -H "Content-Type: application/json" \
  -d '{
    "owner": "<EMAIL>",
    "instanceId": "test-instance",
    "targets": [{
      "entity": "Order",
      "fields": {
        "fieldsHeader": ["_id", "total_products_tax_incl"],
        "fieldsLinee": [{
          "entity": "OrderRow",
          "fields": ["product_name", "quantity_ordered"]
        }]
      },
      "filters": []
    }]
  }'
```

This implementation provides a robust, flexible foundation for handling complex export structures with proper field validation and error handling.
